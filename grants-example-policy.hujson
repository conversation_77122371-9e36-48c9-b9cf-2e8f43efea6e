// Comprehensive example policy file demonstrating Tailscale grants
// Based on https://tailscale.com/kb/1458/grant-examples
{
	// Define user groups
	"groups": {
		"group:admin": ["<EMAIL>"],
		"group:eng": ["<EMAIL>", "<EMAIL>"],
		"group:sales": ["<EMAIL>", "<EMAIL>"],
		"group:nyc": ["<EMAIL>"],
		"group:contractors": ["<EMAIL>"],
		"group:mobile": ["<EMAIL>"]
	},

	// Define host mappings
	"hosts": {
		"web-server": "***********",
		"database": "***********",
		"api-gateway": "***********",
		"logging-server": "***********",
		"internal-tool": "***********",
		"exit-node-nyc": "************"
	},

	// Define tag owners
	"tagOwners": {
		"tag:frontend": ["<EMAIL>", "<EMAIL>"],
		"tag:backend": ["<EMAIL>", "<EMAIL>"],
		"tag:logging": ["<EMAIL>"],
		"tag:internal-tools": ["<EMAIL>"],
		"tag:exit-node-nyc": ["<EMAIL>"],
		"tag:production": ["<EMAIL>"],
		"tag:staging": ["<EMAIL>", "<EMAIL>"]
	},

	// Define device postures
	"postures": {
		"posture:latestMac": [
			"node:os == 'macOS' && node:osVersion >= '13.0'"
		],
		"posture:complianceDevice": [
			"node:tailscaleVersion >= '1.50.0'",
			"node:os in ['macOS', 'Windows', 'Linux']"
		]
	},

	// Modern grants section with comprehensive examples
	"grants": [
		// Example 1: Allow all traffic (basic grant)
		{
			"src": ["*"],
			"dst": ["*"],
			"ip": ["*"]
		},

		// Example 2: User self-access
		{
			"src": ["autogroup:member"],
			"dst": ["autogroup:self"],
			"ip": ["*"]
		},

		// Example 3: Exit node access for internet
		{
			"src": ["autogroup:member"],
			"dst": ["autogroup:internet"],
			"ip": ["*"]
		},

		// Example 4: Tagged component communication (microservices)
		{
			"src": ["tag:frontend"],
			"dst": ["tag:backend"],
			"ip": ["tcp:8080", "tcp:443"]
		},
		{
			"src": ["tag:backend"],
			"dst": ["tag:logging"],
			"ip": ["tcp:514", "udp:514"]
		},

		// Example 5: Group-based access with port restrictions
		{
			"src": ["group:eng"],
			"dst": ["tag:internal-tools"],
			"ip": ["*"]
		},
		{
			"src": ["group:sales"],
			"dst": ["tag:internal-tools"],
			"ip": ["tcp:443", "tcp:22"]
		},

		// Example 6: Location-based exit node routing
		{
			"src": ["group:nyc"],
			"dst": ["autogroup:internet"],
			"via": ["tag:exit-node-nyc"],
			"ip": ["*"]
		},

		// Example 7: Device posture access control
		{
			"src": ["group:mobile"],
			"dst": ["tag:production"],
			"srcPosture": ["posture:latestMac"],
			"ip": ["tcp:443"]
		},

		// Example 8: Application peering with specific protocols
		{
			"src": ["tag:frontend"],
			"dst": ["tag:backend"],
			"app": ["webapp-connector"],
			"ip": ["tcp:8080", "tcp:9090"]
		},

		// Example 9: Admin access with compliance requirements
		{
			"src": ["group:admin"],
			"dst": ["tag:production"],
			"srcPosture": ["posture:complianceDevice"],
			"ip": ["*"]
		},

		// Example 10: Contractor access through jump host
		{
			"src": ["group:contractors"],
			"dst": ["tag:staging"],
			"via": ["internal-tool"],
			"ip": ["tcp:22", "tcp:80"]
		},

		// Example 11: Complex routing with multiple constraints
		{
			"src": ["group:eng"],
			"dst": ["database"],
			"via": ["api-gateway"],
			"srcPosture": ["posture:complianceDevice"],
			"ip": ["tcp:5432", "tcp:3306"]
		},

		// Example 12: Service mesh communication
		{
			"src": ["tag:backend"],
			"dst": ["tag:backend"],
			"app": ["service-mesh"],
			"ip": ["tcp:15000-15010", "tcp:9080"]
		},

		// Example 13: Monitoring access with UDP protocols
		{
			"src": ["tag:logging"],
			"dst": ["tag:frontend", "tag:backend"],
			"ip": ["udp:161", "tcp:9100", "tcp:8080"]
		},

		// Example 14: Emergency admin backdoor
		{
			"src": ["group:admin"],
			"dst": ["*"],
			"ip": ["tcp:22"]
		},

		// Example 15: Cross-environment communication
		{
			"src": ["tag:staging"],
			"dst": ["tag:production"],
			"via": ["api-gateway"],
			"srcPosture": ["posture:complianceDevice"],
			"app": ["deployment-pipeline"],
			"ip": ["tcp:443", "tcp:8443"]
		}
	]
}